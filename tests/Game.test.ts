import { Game } from '../src/Game';

describe('Game', () => {
    let game: Game;

    beforeEach(() => {
        jest.clearAllMocks();
        game = new Game();
    });

    describe('findHighestLogIdForPacket', () => {
        it('should return 0 when logToMoveId is empty', () => {
            const result = (game as any).findHighestLogIdForPacket({}, 100);
            expect(result).toBe(0);
        });

        it('should return the highest log ID for exact packet match', () => {
            const logToMoveId = {
                '10': 100,
                '15': 100,
                '20': 100,
                '25': 200
            };
            
            const result = (game as any).findHighestLogIdForPacket(logToMoveId, 100);
            expect(result).toBe(20);
        });

        it('should return log ID for fallback when no exact match exists', () => {
            const logToMoveId = {
                '10': 50,
                '15': 75,
                '20': 90,
                '25': 150
            };
            
            const result = (game as any).findHighestLogIdForPacket(logToMoveId, 100);
            expect(result).toBe(20); // packet 90 is highest <= 100
        });

        it('should prefer exact match over fallback', () => {
            const logToMoveId = {
                '10': 50,
                '15': 75,
                '20': 100,
                '25': 90
            };
            
            const result = (game as any).findHighestLogIdForPacket(logToMoveId, 100);
            expect(result).toBe(20); // exact match for packet 100
        });

        it('should handle string values in logToMoveId', () => {
            const logToMoveId = {
                '10': '100',
                '15': '100',
                '20': '200'
            };
            
            const result = (game as any).findHighestLogIdForPacket(logToMoveId, 100);
            expect(result).toBe(15);
        });

        it('should handle mixed string and number values', () => {
            const logToMoveId = {
                '10': 100,
                '15': '100',
                '20': 200
            };
            
            const result = (game as any).findHighestLogIdForPacket(logToMoveId, 100);
            expect(result).toBe(15);
        });

        it('should return 0 when no packet IDs are <= target', () => {
            const logToMoveId = {
                '10': 200,
                '15': 300,
                '20': 400
            };
            
            const result = (game as any).findHighestLogIdForPacket(logToMoveId, 100);
            expect(result).toBe(0);
        });

        it('should handle target packet ID as string', () => {
            const logToMoveId = {
                '10': 100,
                '15': 200
            };
            
            const result = (game as any).findHighestLogIdForPacket(logToMoveId, '100' as any);
            expect(result).toBe(10);
        });

        it('should handle complex scenario with multiple exact matches and fallbacks', () => {
            const logToMoveId = {
                '5': 50,
                '10': 100,
                '12': 100,
                '15': 100,
                '20': 90,
                '25': 110,
                '30': 200
            };
            
            const result = (game as any).findHighestLogIdForPacket(logToMoveId, 100);
            expect(result).toBe(15); // highest log ID for exact packet match 100
        });

        it('should handle edge case with packet ID 0', () => {
            const logToMoveId = {
                '10': 0,
                '15': 50
            };
            
            const result = (game as any).findHighestLogIdForPacket(logToMoveId, 0);
            expect(result).toBe(10);
        });
    });
});
