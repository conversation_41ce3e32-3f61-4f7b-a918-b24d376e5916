const path = require('path');

const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  entry: './azul.ts',
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  resolve: {
    extensions: ['.ts', '.js'],
  },
  output: {
    filename: 'azul.js',
    path: path.resolve(__dirname, '.'),
  },
  mode: isProduction ? 'production' : 'development',
  optimization: {
    minimize: isProduction
  }
};
